import Benefits from '@components/Benefits';
import CareersHeroSection from '@components/CareersHeroSection';
import CoreValues from '@components/CoreValues';
import EmployeeTestimonial from '@components/EmployeeTestimonial';
import GPTW from '@components/GPTW';
import LifeAtMtl from '@components/LifeAtMtl';
import getSectionId from '@utils/getSectionId';
import seoSchema from '@utils/seoSchema';
import fetchFromStrapi from '@utils/fetchFromStrapi';
import RichResults from '@components/RichResults';
import HashNavigationWrapper from '@components/HashNavigationWrapper/HashNavigationWrapper';

async function fetchFooterData() {
  const query = `populate=sector_row.Sublinks,pages_row.Sublinks,terms_and_condition_section,company_logo_section.image,company_logo_section.Copyright,company_logo_section.social_platforms.image`;
  return await fetchFromStrapi('footer', query);
}

export async function fetchCareersPageData() {
  const queryString = `populate=hero_section.button&populate=life_at_mtl.images&populate=gptw.image&populate=employee_testimonial.employee_box.emp_image&populate=benefits.first_slider.image&populate=benefits.second_slider.image&populate=core_values.box.image,seo.schema`;
  return await fetchFromStrapi('career', queryString);
}

export async function generateMetadata() {
  const queryString = `populate=seo&populate=seo.image&populate=seo.metaProperties&populate=seo.keywords&populate=seo.schema`;
  const seoFetchedData = await fetchFromStrapi('career', queryString);
  const seoData = seoFetchedData?.data?.attributes?.seo;
  return seoSchema(seoData);
}

export default async function Careers() {
  const footerData = await fetchFooterData();
  const careersPageData = await fetchCareersPageData();

  // Extract section IDs from footer data, with fallbacks for consistent navigation
  const currentOpportunitiesRefLink = getSectionId(
    footerData?.data?.attributes?.sector_row[2]?.Sublinks[0]?.link,
  ) || 'current-opportunities';
  const employeeTestimonialsRefLink = getSectionId(
    footerData?.data?.attributes?.sector_row[2]?.Sublinks[1]?.link,
  ) || 'employee-testimonials';
  const coreValuesRefLink = getSectionId(
    footerData?.data?.attributes?.sector_row[2]?.Sublinks[2]?.link,
  ) || 'core-values';
  const benefitsRefLink = getSectionId(
    footerData?.data?.attributes?.sector_row[2]?.Sublinks[3]?.link,
  ) || 'benefits';
  const lifeAtMtlRefLink = getSectionId(
    footerData?.data?.attributes?.sector_row[2]?.Sublinks[4]?.link,
  ) || 'life-at-mtl';

  return (
    <HashNavigationWrapper>
      {careersPageData?.data?.attributes?.seo && (
        <RichResults data={careersPageData?.data?.attributes?.seo} />
      )}
      {careersPageData?.data?.attributes?.hero_section && (
        <div id={currentOpportunitiesRefLink}>
          <CareersHeroSection
            CareersHeroSectionData={
              careersPageData?.data?.attributes?.hero_section
            }
          />
        </div>
      )}
      {careersPageData?.data?.attributes?.gptw && (
        <GPTW GPTWData={careersPageData?.data?.attributes?.gptw} />
      )}
      {careersPageData?.data?.attributes?.core_values && (
        <div id={coreValuesRefLink}>
          <CoreValues
            coreValuesData={careersPageData?.data?.attributes?.core_values}
          />
        </div>
      )}
      {careersPageData?.data?.attributes?.life_at_mtl && (
        <div id={lifeAtMtlRefLink}>
          <LifeAtMtl
            LifeAtMtlData={careersPageData?.data?.attributes?.life_at_mtl}
          />
        </div>
      )}
      {careersPageData?.data?.attributes?.benefits && (
        <div id={benefitsRefLink}>
          <Benefits
            BenefitsData={careersPageData?.data?.attributes?.benefits}
          />
        </div>
      )}
      {careersPageData?.data?.attributes?.employee_testimonial && (
        <div id={employeeTestimonialsRefLink}>
          <EmployeeTestimonial
            EmployeeTestimonialData={
              careersPageData?.data?.attributes?.employee_testimonial
            }
          />
        </div>
      )}
    </HashNavigationWrapper>
  );
}
