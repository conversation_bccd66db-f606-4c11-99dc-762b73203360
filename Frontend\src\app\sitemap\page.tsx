import React from 'react';
import Link from 'next/link';
import { categorizeSitemapUrls, getBaseUrl } from '@utils/sitemapUtils';
import seoSchema from '@utils/seoSchema';
import styles from './sitemap.module.css';

export const dynamic = 'force-static';

export async function generateMetadata() {
  const baseUrl = getBaseUrl();

  const seoData = {
    metaTitle: 'Sitemap - Maruti Techlabs',
    metaDescription:
      'Complete sitemap of Maruti Techlabs website including all services, industries, resources, case studies, blog posts, and other pages.',
    metaKeywords: [
      { keyword: 'sitemap' },
      { keyword: 'website navigation' },
      { keyword: 'maruti techlabs pages' },
      { keyword: 'site structure' },
    ],
    canonicalURL: `${baseUrl}/sitemap`,
    metaImage: {
      data: {
        attributes: {
          url: `${process.env.NEXT_PUBLIC_CLOUDFRONT_URL}/marutitech_logo_fe5e4e6162.svg`,
        },
      },
    },
  };

  return seoSchema(seoData);
}

async function SitemapPage() {
  const categories = await categorizeSitemapUrls();
  const baseUrl = getBaseUrl();

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <h1 className={styles.title}>Sitemap</h1>
        <p className={styles.description}>
          Complete navigation guide to all pages on our website. Find everything
          from our services and industries to resources and case studies.
        </p>
      </div>

      <div className={styles.sitemapContent}>
        {categories.map((category, index) => (
          <div key={index} className={styles.categorySection}>
            <h2 className={styles.categoryTitle}>{category.name}</h2>
            <ul className={styles.pageList}>
              {category.urls.map((url, urlIndex) => {
                const path = url.loc.replace(baseUrl, '') || '/';
                const displayName = getDisplayName(path);

                return (
                  <li key={urlIndex} className={styles.pageItem}>
                    <Link href={path} className={styles.pageLink}>
                      {displayName}
                    </Link>
                  </li>
                );
              })}
            </ul>
          </div>
        ))}
      </div>

      <div className={styles.footer}>
        <p className={styles.lastUpdated}>
          Last updated:{' '}
          {new Date().toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
          })}
        </p>
      </div>
    </div>
  );
}

/**
 * Generate display name from URL path
 */
function getDisplayName(path: string): string {
  if (path === '/') return 'Homepage';

  // Remove leading slash and split by slash
  const segments = path.slice(1).split('/');

  // Take the last segment for display
  const lastSegment = segments[segments.length - 1];

  // Convert slug to readable name
  return lastSegment
    .split('-')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
}

export default SitemapPage;
