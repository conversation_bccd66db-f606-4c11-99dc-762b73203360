import AllServicePage from './AllServicePage';

export default {
  title: 'Components/All Service Page',
};

const data = {
  data: {
    id: 1,
    attributes: {
      createdAt: '2024-08-20T11:15:36.778Z',
      updatedAt: '2024-08-23T08:46:28.330Z',
      publishedAt: '2024-08-20T11:15:37.970Z',
      all_service_page: [
        {
          id: 2,
          tag: '01',
          title: 'Product Engineering',
          description:
            '<p>Maruti AI solutions help you turbocharge software development, reduce manual operations, lower costs, spur faster revenue growth and enhance customer experiences.</p>',
          l_2_service_pages: {
            data: [
              {
                id: 1,
                attributes: {
                  createdAt: '2024-07-02T12:43:04.644Z',
                  updatedAt: '2024-08-21T04:46:36.065Z',
                  publishedAt: '2024-07-02T12:43:05.548Z',
                  pageName: 'Software Product Engineering',
                  slug: 'software-product-engineering-services',
                },
              },
              {
                id: 2,
                attributes: {
                  createdAt: '2024-08-22T13:23:18.201Z',
                  updatedAt: '2024-08-22T13:23:21.621Z',
                  publishedAt: '2024-08-22T13:23:21.610Z',
                  pageName: 'Demo',
                  slug: 'demo',
                },
              },
            ],
          },
          button: {
            id: 12,
            title: 'Our Offerings',
            link: 'abc',
          },
          image: {
            data: {
              id: 73,
              attributes: {
                name: 'vison_mission.png',
                alternativeText: null,
                caption: null,
                width: 720,
                height: 500,
                formats: {
                  thumbnail: {
                    name: 'thumbnail_vison_mission.png',
                    hash: 'thumbnail_vison_mission_0bc7851090',
                    ext: '.png',
                    mime: 'image/png',
                    path: null,
                    width: 225,
                    height: 156,
                    size: 88.94,
                    sizeInBytes: 88935,
                    url: `${process.env.NEXT_PUBLIC_CLOUDFRONT_URL}/thumbnail_vison_mission_0bc7851090.png`,
                  },
                  small: {
                    name: 'small_vison_mission.png',
                    hash: 'small_vison_mission_0bc7851090',
                    ext: '.png',
                    mime: 'image/png',
                    path: null,
                    width: 500,
                    height: 347,
                    size: 399.84,
                    sizeInBytes: 399842,
                    url: `${process.env.NEXT_PUBLIC_CLOUDFRONT_URL}/small_vison_mission_0bc7851090.png`,
                  },
                },
                hash: 'vison_mission_0bc7851090',
                ext: '.png',
                mime: 'image/png',
                size: 135.39,
                url: `${process.env.NEXT_PUBLIC_CLOUDFRONT_URL}/vison_mission_0bc7851090.png`,
                previewUrl: null,
                provider:
                  '@strapi-community/strapi-provider-upload-google-cloud-storage',
                provider_metadata: null,
                createdAt: '2024-08-08T12:23:07.446Z',
                updatedAt: '2024-08-08T12:23:07.446Z',
              },
            },
          },
        },
        {
          id: 1,
          tag: '02',
          title: 'DevOps & Cloud Engineering',
          description:
            '<p>Maruti AI solutions help you turbocharge software development, reduce manual operations, lower costs, spur faster revenue growth and enhance customer experiences.</p>',
          l_2_service_pages: {
            data: [
              {
                id: 1,
                attributes: {
                  createdAt: '2024-07-02T12:43:04.644Z',
                  updatedAt: '2024-08-21T04:46:36.065Z',
                  publishedAt: '2024-07-02T12:43:05.548Z',
                  pageName: 'Software Product Engineering',
                  slug: 'software-product-engineering-services',
                },
              },
              {
                id: 2,
                attributes: {
                  createdAt: '2024-08-22T13:23:18.201Z',
                  updatedAt: '2024-08-22T13:23:21.621Z',
                  publishedAt: '2024-08-22T13:23:21.610Z',
                  pageName: 'Demo',
                  slug: 'demo',
                },
              },
            ],
          },
          button: {
            id: 15,
            title: 'Our Offerings',
            link: 'abc',
          },
          image: {
            data: {
              id: 73,
              attributes: {
                name: 'vison_mission.png',
                alternativeText: null,
                caption: null,
                width: 720,
                height: 500,
                formats: {
                  thumbnail: {
                    name: 'thumbnail_vison_mission.png',
                    hash: 'thumbnail_vison_mission_0bc7851090',
                    ext: '.png',
                    mime: 'image/png',
                    path: null,
                    width: 225,
                    height: 156,
                    size: 88.94,
                    sizeInBytes: 88935,
                    url: `${process.env.NEXT_PUBLIC_CLOUDFRONT_URL}/thumbnail_vison_mission_0bc7851090.png`,
                  },
                  small: {
                    name: 'small_vison_mission.png',
                    hash: 'small_vison_mission_0bc7851090',
                    ext: '.png',
                    mime: 'image/png',
                    path: null,
                    width: 500,
                    height: 347,
                    size: 399.84,
                    sizeInBytes: 399842,
                    url: `${process.env.NEXT_PUBLIC_CLOUDFRONT_URL}/small_vison_mission_0bc7851090.png`,
                  },
                },
                hash: 'vison_mission_0bc7851090',
                ext: '.png',
                mime: 'image/png',
                size: 135.39,
                url: `${process.env.NEXT_PUBLIC_CLOUDFRONT_URL}/vison_mission_0bc7851090.png`,
                previewUrl: null,
                provider:
                  '@strapi-community/strapi-provider-upload-google-cloud-storage',
                provider_metadata: null,
                createdAt: '2024-08-08T12:23:07.446Z',
                updatedAt: '2024-08-08T12:23:07.446Z',
              },
            },
          },
        },
        {
          id: 3,
          tag: '03',
          title: 'Data, Analytics & AI',
          description:
            '<p>Maruti AI solutions help you turbocharge software development, reduce manual operations, lower costs, spur faster revenue growth and enhance customer experiences.</p>',
          l_2_service_pages: {
            data: [
              {
                id: 1,
                attributes: {
                  createdAt: '2024-07-02T12:43:04.644Z',
                  updatedAt: '2024-08-21T04:46:36.065Z',
                  publishedAt: '2024-07-02T12:43:05.548Z',
                  pageName: 'Software Product Engineering',
                  slug: 'software-product-engineering-services',
                },
              },
              {
                id: 2,
                attributes: {
                  createdAt: '2024-08-22T13:23:18.201Z',
                  updatedAt: '2024-08-22T13:23:21.621Z',
                  publishedAt: '2024-08-22T13:23:21.610Z',
                  pageName: 'Demo',
                  slug: 'demo',
                },
              },
            ],
          },
          button: {
            id: 13,
            title: 'Our Offerings',
            link: 'abc',
          },
          image: {
            data: {
              id: 73,
              attributes: {
                name: 'vison_mission.png',
                alternativeText: null,
                caption: null,
                width: 720,
                height: 500,
                formats: {
                  thumbnail: {
                    name: 'thumbnail_vison_mission.png',
                    hash: 'thumbnail_vison_mission_0bc7851090',
                    ext: '.png',
                    mime: 'image/png',
                    path: null,
                    width: 225,
                    height: 156,
                    size: 88.94,
                    sizeInBytes: 88935,
                    url: `${process.env.NEXT_PUBLIC_CLOUDFRONT_URL}/thumbnail_vison_mission_0bc7851090.png`,
                  },
                  small: {
                    name: 'small_vison_mission.png',
                    hash: 'small_vison_mission_0bc7851090',
                    ext: '.png',
                    mime: 'image/png',
                    path: null,
                    width: 500,
                    height: 347,
                    size: 399.84,
                    sizeInBytes: 399842,
                    url: `${process.env.NEXT_PUBLIC_CLOUDFRONT_URL}/small_vison_mission_0bc7851090.png`,
                  },
                },
                hash: 'vison_mission_0bc7851090',
                ext: '.png',
                mime: 'image/png',
                size: 135.39,
                url: `${process.env.NEXT_PUBLIC_CLOUDFRONT_URL}/vison_mission_0bc7851090.png`,
                previewUrl: null,
                provider:
                  '@strapi-community/strapi-provider-upload-google-cloud-storage',
                provider_metadata: null,
                createdAt: '2024-08-08T12:23:07.446Z',
                updatedAt: '2024-08-08T12:23:07.446Z',
              },
            },
          },
        },
        {
          id: 4,
          tag: '04',
          title: 'Customer Experience',
          description:
            '<p>Maruti AI solutions help you turbocharge software development, reduce manual operations, lower costs, spur faster revenue growth and enhance customer experiences.</p>',
          l_2_service_pages: {
            data: [
              {
                id: 1,
                attributes: {
                  createdAt: '2024-07-02T12:43:04.644Z',
                  updatedAt: '2024-08-21T04:46:36.065Z',
                  publishedAt: '2024-07-02T12:43:05.548Z',
                  pageName: 'Software Product Engineering',
                  slug: 'software-product-engineering-services',
                },
              },
              {
                id: 2,
                attributes: {
                  createdAt: '2024-08-22T13:23:18.201Z',
                  updatedAt: '2024-08-22T13:23:21.621Z',
                  publishedAt: '2024-08-22T13:23:21.610Z',
                  pageName: 'Demo',
                  slug: 'demo',
                },
              },
            ],
          },
          button: {
            id: 14,
            title: 'Our Offerings',
            link: 'abc',
          },
          image: {
            data: {
              id: 73,
              attributes: {
                name: 'vison_mission.png',
                alternativeText: null,
                caption: null,
                width: 720,
                height: 500,
                formats: {
                  thumbnail: {
                    name: 'thumbnail_vison_mission.png',
                    hash: 'thumbnail_vison_mission_0bc7851090',
                    ext: '.png',
                    mime: 'image/png',
                    path: null,
                    width: 225,
                    height: 156,
                    size: 88.94,
                    sizeInBytes: 88935,
                    url: `${process.env.NEXT_PUBLIC_CLOUDFRONT_URL}/thumbnail_vison_mission_0bc7851090.png`,
                  },
                  small: {
                    name: 'small_vison_mission.png',
                    hash: 'small_vison_mission_0bc7851090',
                    ext: '.png',
                    mime: 'image/png',
                    path: null,
                    width: 500,
                    height: 347,
                    size: 399.84,
                    sizeInBytes: 399842,
                    url: `${process.env.NEXT_PUBLIC_CLOUDFRONT_URL}/small_vison_mission_0bc7851090.png`,
                  },
                },
                hash: 'vison_mission_0bc7851090',
                ext: '.png',
                mime: 'image/png',
                size: 135.39,
                url: `${process.env.NEXT_PUBLIC_CLOUDFRONT_URL}/vison_mission_0bc7851090.png`,
                previewUrl: null,
                provider:
                  '@strapi-community/strapi-provider-upload-google-cloud-storage',
                provider_metadata: null,
                createdAt: '2024-08-08T12:23:07.446Z',
                updatedAt: '2024-08-08T12:23:07.446Z',
              },
            },
          },
        },
        {
          id: 5,
          tag: '05',
          title: 'Digital & Technology Consulting',
          description:
            '<p>Maruti AI solutions help you turbocharge software development, reduce manual operations, lower costs, spur faster revenue growth and enhance customer experiences.</p>',
          l_2_service_pages: {
            data: [
              {
                id: 1,
                attributes: {
                  createdAt: '2024-07-02T12:43:04.644Z',
                  updatedAt: '2024-08-21T04:46:36.065Z',
                  publishedAt: '2024-07-02T12:43:05.548Z',
                  pageName: 'Software Product Engineering',
                  slug: 'software-product-engineering-services',
                },
              },
            ],
          },
          button: {
            id: 16,
            title: 'Our Offerings',
            link: 'abc',
          },
          image: {
            data: {
              id: 73,
              attributes: {
                name: 'vison_mission.png',
                alternativeText: null,
                caption: null,
                width: 720,
                height: 500,
                formats: {
                  thumbnail: {
                    name: 'thumbnail_vison_mission.png',
                    hash: 'thumbnail_vison_mission_0bc7851090',
                    ext: '.png',
                    mime: 'image/png',
                    path: null,
                    width: 225,
                    height: 156,
                    size: 88.94,
                    sizeInBytes: 88935,
                    url: `${process.env.NEXT_PUBLIC_CLOUDFRONT_URL}/thumbnail_vison_mission_0bc7851090.png`,
                  },
                  small: {
                    name: 'small_vison_mission.png',
                    hash: 'small_vison_mission_0bc7851090',
                    ext: '.png',
                    mime: 'image/png',
                    path: null,
                    width: 500,
                    height: 347,
                    size: 399.84,
                    sizeInBytes: 399842,
                    url: `${process.env.NEXT_PUBLIC_CLOUDFRONT_URL}/small_vison_mission_0bc7851090.png`,
                  },
                },
                hash: 'vison_mission_0bc7851090',
                ext: '.png',
                mime: 'image/png',
                size: 135.39,
                url: `${process.env.NEXT_PUBLIC_CLOUDFRONT_URL}/vison_mission_0bc7851090.png`,
                previewUrl: null,
                provider:
                  '@strapi-community/strapi-provider-upload-google-cloud-storage',
                provider_metadata: null,
                createdAt: '2024-08-08T12:23:07.446Z',
                updatedAt: '2024-08-08T12:23:07.446Z',
              },
            },
          },
        },
        {
          id: 6,
          tag: '06',
          title: 'Talent Solutions',
          description:
            '<p>Maruti AI solutions help you turbocharge software development, reduce manual operations, lower costs, spur faster revenue growth and enhance customer experiences.</p>',
          l_2_service_pages: {
            data: [
              {
                id: 1,
                attributes: {
                  createdAt: '2024-07-02T12:43:04.644Z',
                  updatedAt: '2024-08-21T04:46:36.065Z',
                  publishedAt: '2024-07-02T12:43:05.548Z',
                  pageName: 'Software Product Engineering',
                  slug: 'software-product-engineering-services',
                },
              },
            ],
          },
          button: {
            id: 17,
            title: 'Our Offerings',
            link: 'abc',
          },
          image: {
            data: {
              id: 73,
              attributes: {
                name: 'vison_mission.png',
                alternativeText: null,
                caption: null,
                width: 720,
                height: 500,
                formats: {
                  thumbnail: {
                    name: 'thumbnail_vison_mission.png',
                    hash: 'thumbnail_vison_mission_0bc7851090',
                    ext: '.png',
                    mime: 'image/png',
                    path: null,
                    width: 225,
                    height: 156,
                    size: 88.94,
                    sizeInBytes: 88935,
                    url: `${process.env.NEXT_PUBLIC_CLOUDFRONT_URL}/thumbnail_vison_mission_0bc7851090.png`,
                  },
                  small: {
                    name: 'small_vison_mission.png',
                    hash: 'small_vison_mission_0bc7851090',
                    ext: '.png',
                    mime: 'image/png',
                    path: null,
                    width: 500,
                    height: 347,
                    size: 399.84,
                    sizeInBytes: 399842,
                    url: `${process.env.NEXT_PUBLIC_CLOUDFRONT_URL}/small_vison_mission_0bc7851090.png`,
                  },
                },
                hash: 'vison_mission_0bc7851090',
                ext: '.png',
                mime: 'image/png',
                size: 135.39,
                url: `${process.env.NEXT_PUBLIC_CLOUDFRONT_URL}/vison_mission_0bc7851090.png`,
                previewUrl: null,
                provider:
                  '@strapi-community/strapi-provider-upload-google-cloud-storage',
                provider_metadata: null,
                createdAt: '2024-08-08T12:23:07.446Z',
                updatedAt: '2024-08-08T12:23:07.446Z',
              },
            },
          },
        },
      ],
    },
  },
  meta: {},
};

export function AllServicePageStory() {
  return (
    <>
      <AllServicePage
        allServicePageData={data?.data?.attributes?.all_service_page}
      />
    </>
  );
}
