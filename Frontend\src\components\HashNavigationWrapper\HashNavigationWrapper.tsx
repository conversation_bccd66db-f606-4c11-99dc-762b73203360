'use client';

import { ReactNode, useEffect, useRef } from 'react';
import { usePathname } from 'next/navigation';
import useHashNavigation from '@hooks/useHashNavigation';
import { initializeHashNavigation } from '@utils/hashNavigationFallback';

interface HashNavigationWrapperProps {
  children: ReactNode;
}

/**
 * Enhanced client-side wrapper component that enables robust hash navigation for static sites
 * This component combines the standard hash navigation hook with fallback strategies
 * for better reliability on static hosted sites
 */
export default function HashNavigationWrapper({
  children,
}: HashNavigationWrapperProps) {
  const pathname = usePathname();
  const lastPathnameRef = useRef(pathname);
  const hashProcessedRef = useRef(false);

  // Initialize the standard hash navigation hook
  useHashNavigation();

  // Handle route changes and hash navigation
  useEffect(() => {
    // Check if this is a new page navigation
    const isNewPage = lastPathnameRef.current !== pathname;
    lastPathnameRef.current = pathname;

    if (isNewPage) {
      // Reset hash processing flag for new pages
      hashProcessedRef.current = false;
    }

    // Initialize comprehensive hash navigation with fallbacks
    // Enable debug mode in development
    const isDebug =
      process.env.NODE_ENV === 'development' ||
      (typeof window !== 'undefined' &&
        window.location.hostname === 'localhost');

    // Add a small delay for new page navigations to ensure DOM is ready
    const initDelay = isNewPage ? 100 : 0;

    setTimeout(() => {
      initializeHashNavigation(isDebug);
    }, initDelay);

    // Handle hash navigation on route changes
    if (isNewPage && typeof window !== 'undefined') {
      const currentHash = window.location.hash.substring(1);
      if (currentHash && !hashProcessedRef.current) {
        hashProcessedRef.current = true;

        // Multiple strategies for hash navigation after route change
        const handleRouteChangeHash = () => {
          const element = document.getElementById(currentHash);
          if (element) {
            setTimeout(() => {
              element.scrollIntoView({
                behavior: 'smooth',
                block: 'start',
              });
            }, 200);
          }
        };

        // Try immediately
        handleRouteChangeHash();

        // Try after DOM content loaded
        if (document.readyState === 'loading') {
          document.addEventListener('DOMContentLoaded', handleRouteChangeHash);
        }

        // Try after a delay to ensure React hydration
        setTimeout(handleRouteChangeHash, 500);
        setTimeout(handleRouteChangeHash, 1000);
      }
    }
  }, [pathname]);

  // Additional effect to handle hash changes within the same page
  useEffect(() => {
    const handleHashChange = () => {
      hashProcessedRef.current = false; // Reset flag on hash change
    };

    if (typeof window !== 'undefined') {
      window.addEventListener('hashchange', handleHashChange);
      return () => {
        window.removeEventListener('hashchange', handleHashChange);
      };
    }
  }, []);

  return <>{children}</>;
}
