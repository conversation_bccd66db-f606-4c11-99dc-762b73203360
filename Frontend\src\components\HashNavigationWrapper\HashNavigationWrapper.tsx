'use client';

import { ReactNode, useEffect, useRef, useState } from 'react';
import { usePathname } from 'next/navigation';
import useHashNavigation from '@hooks/useHashNavigation';
import { initializeHashNavigation } from '@utils/hashNavigationFallback';

interface HashNavigationWrapperProps {
  children: ReactNode;
}

/**
 * Enhanced client-side wrapper component that enables robust hash navigation for static sites
 * This component combines the standard hash navigation hook with fallback strategies
 * for better reliability on static hosted sites
 *
 * Based on the successful AI readiness audit component approach
 */
export default function HashNavigationWrapper({
  children,
}: HashNavigationWrapperProps) {
  const pathname = usePathname();
  const lastPathnameRef = useRef(pathname);
  const hashProcessedRef = useRef(false);
  const [isClient, setIsClient] = useState(false);

  // Initialize the standard hash navigation hook
  useHashNavigation();

  // Ensure we're on the client side
  useEffect(() => {
    setIsClient(true);
  }, []);

  // Enhanced hash change handler based on AI readiness audit pattern
  useEffect(() => {
    if (!isClient || typeof window === 'undefined') return;

    function handleHashChange() {
      // Use the same pattern as the working useHashNavigation hook
      const currentHash = (window.location.hash as any).substring(1);

      if (!currentHash) return;

      // Use the same approach as AI readiness audit component
      const element = document.getElementById(currentHash);
      if (element) {
        // Smooth scroll to the element
        element.scrollIntoView({
          behavior: 'smooth',
          block: 'start'
        });
      } else {
        // Retry after a short delay for dynamically rendered content
        setTimeout(() => {
          const retryElement = document.getElementById(currentHash);
          if (retryElement) {
            retryElement.scrollIntoView({
              behavior: 'smooth',
              block: 'start'
            });
          }
        }, 100);
      }
    }

    // Handle initial hash on component mount
    handleHashChange();

    // Listen for hash changes
    window.addEventListener('hashchange', handleHashChange);

    return () => {
      window.removeEventListener('hashchange', handleHashChange);
    };
  }, [isClient]);

  // Handle route changes and hash navigation
  useEffect(() => {
    if (!isClient) return;

    // Check if this is a new page navigation
    const isNewPage = lastPathnameRef.current !== pathname;
    lastPathnameRef.current = pathname;

    if (isNewPage) {
      // Reset hash processing flag for new pages
      hashProcessedRef.current = false;
    }

    // Initialize comprehensive hash navigation with fallbacks
    // Enable debug mode in development
    const isDev = typeof window !== 'undefined' && window.location.hostname === 'localhost';

    // Add a small delay for new page navigations to ensure DOM is ready
    const initDelay = isNewPage ? 100 : 0;

    setTimeout(() => {
      initializeHashNavigation(isDev);
    }, initDelay);

    // Handle hash navigation on route changes
    if (isNewPage && typeof window !== 'undefined') {
      const currentHash = (window.location.hash as any).substring(1);
      if (currentHash && !hashProcessedRef.current) {
        hashProcessedRef.current = true;

        // Multiple strategies for hash navigation after route change
        const handleRouteChangeHash = () => {
          const element = document.getElementById(currentHash);
          if (element) {
            setTimeout(() => {
              element.scrollIntoView({
                behavior: 'smooth',
                block: 'start',
              });
            }, 200);
          }
        };

        // Try immediately
        handleRouteChangeHash();

        // Try after DOM content loaded
        if (document.readyState === 'loading') {
          document.addEventListener('DOMContentLoaded', handleRouteChangeHash);
        }

        // Try after a delay to ensure React hydration
        setTimeout(handleRouteChangeHash, 500);
        setTimeout(handleRouteChangeHash, 1000);
      }
    }
  }, [pathname, isClient]);

  return <>{children}</>;
}
