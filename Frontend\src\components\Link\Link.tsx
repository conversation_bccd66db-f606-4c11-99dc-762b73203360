import React, { CSSProperties, ReactEventHandler, ReactNode } from 'react';
import NextLink from 'next/link';
import { usePathname } from 'next/navigation';
import classNames from '@utils/classNames';

interface LinkProps {
  id?: string;
  children?: ReactNode;
  href?: string;
  className?: string;
  style?: CSSProperties;
  isExternal?: boolean;
  onClick?: ReactEventHandler;
  scroll?: boolean;
  shallow?: boolean;
  ariaLabel?: string;
  dataID?: string;
  suppressHydrationWarning?: boolean;
}

export default function Link({
  id,
  children,
  href = '',
  className,
  style,
  isExternal = false,
  onClick,
  scroll = true,
  shallow = false,
  ariaLabel,
  dataID = null,
  suppressHydrationWarning = false,
}: LinkProps) {
  const pathname = usePathname();

  // Check if this is a hash-only link to the current page
  const isHashOnlyLink =
    href.includes('#') &&
    (href.startsWith('#') ||
      href.startsWith(pathname + '#') ||
      href === pathname + href.substring(href.indexOf('#')));

  // For hash-only links to the current page, use regular anchor tag
  // to avoid Next.js router interference
  if (isHashOnlyLink && !isExternal) {
    const handleHashClick = (e: React.MouseEvent) => {
      // Let the default behavior handle the hash navigation
      // but also call the onClick handler if provided
      if (onClick) {
        onClick(e);
      }

      // Extract hash from href
      const hash = href.includes('#')
        ? href.substring(href.indexOf('#') + 1)
        : '';
      if (hash) {
        // Small delay to ensure the hash is updated in the URL first
        setTimeout(() => {
          const element = document.getElementById(hash);
          if (element) {
            element.scrollIntoView({
              behavior: 'smooth',
              block: 'start',
            });
          }
        }, 10);
      }
    };

    return (
      <a
        suppressHydrationWarning={suppressHydrationWarning}
        id={id}
        className={classNames(className)}
        href={href}
        style={style}
        aria-label={ariaLabel}
        onClick={handleHashClick}
        data-id={dataID}
      >
        {children}
      </a>
    );
  }

  return isExternal ? (
    <a
      suppressHydrationWarning={suppressHydrationWarning}
      id={id}
      className={classNames(className)}
      href={href}
      style={style}
      target="_blank"
      rel="noreferrer"
      aria-label={ariaLabel}
      onClick={onClick}
      data-id={dataID}
    >
      {children}
    </a>
  ) : (
    <NextLink
      href={href}
      scroll={scroll}
      shallow={shallow}
      suppressHydrationWarning={suppressHydrationWarning}
      id={id}
      className={classNames(className)}
      style={style}
      onClick={onClick}
      data-id={dataID}
      aria-label={ariaLabel}
    >
      {children}
    </NextLink>
  );
}
