'use client';

import { useEffect, useCallback } from 'react';

/**
 * Custom hook for handling hash navigation and smooth scrolling
 * Optimized for static sites with proper timing and retry logic
 */
export default function useHashNavigation() {
  const scrollToElement = useCallback((elementId: string, retryCount = 0) => {
    if (typeof window === 'undefined') return;

    const element = document.getElementById(elementId);

    if (element) {
      // Ensure the element is visible and has dimensions
      const rect = element.getBoundingClientRect();
      if (rect.height > 0 && rect.width > 0) {
        // Add a small delay to ensure the element is fully rendered
        setTimeout(() => {
          element.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
          });
        }, 100);
      } else if (retryCount < 5) {
        // Element exists but not visible yet, retry
        setTimeout(() => {
          scrollToElement(elementId, retryCount + 1);
        }, 200 * (retryCount + 1));
      }
    } else if (retryCount < 8) {
      // Retry finding the element after a short delay
      // This handles cases where the DOM is still being hydrated
      const delay = retryCount < 3 ? 200 : 500; // Faster retries initially
      setTimeout(() => {
        scrollToElement(elementId, retryCount + 1);
      }, delay * (retryCount + 1));
    } else {
      // Final fallback: try to scroll to any element with similar ID patterns
      const fallbackElement = document.querySelector(`[id*="${elementId}"]`) as HTMLElement;
      if (fallbackElement) {
        setTimeout(() => {
          fallbackElement.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
          });
        }, 100);
      }
    }
  }, []);

  const handleHashChange = useCallback(() => {
    if (typeof window === 'undefined') return;

    const currentHash = window.location.hash.substring(1);

    if (currentHash) {
      // Use the same approach as AI readiness audit component
      const element = document.getElementById(currentHash);
      if (element) {
        // Immediate scroll if element is found
        element.scrollIntoView({
          behavior: 'smooth',
          block: 'start'
        });
      } else {
        // Fallback to the retry mechanism for dynamically rendered content
        setTimeout(() => {
          scrollToElement(currentHash);
        }, 50);
      }
    }
  }, [scrollToElement]);

  useEffect(() => {
    let mutationObserver: MutationObserver | null = null;

    // Handle initial hash on component mount with multiple strategies
    const handleInitialHash = () => {
      // Strategy 1: Immediate attempt
      handleHashChange();

      // Strategy 2: After DOM content loaded (for static sites)
      if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', handleHashChange);
      }

      // Strategy 3: After a delay to ensure React hydration is complete
      setTimeout(handleHashChange, 500);

      // Strategy 4: After images and other resources load
      window.addEventListener('load', handleHashChange);

      // Strategy 5: Watch for DOM changes (for dynamically rendered content)
      const currentHash = window.location.hash.substring(1);
      if (currentHash && !document.getElementById(currentHash)) {
        mutationObserver = new MutationObserver((mutations) => {
          mutations.forEach((mutation) => {
            if (mutation.type === 'childList') {
              const addedNodes = Array.from(mutation.addedNodes);
              const hasTargetElement = addedNodes.some(node => {
                if (node.nodeType === Node.ELEMENT_NODE) {
                  const element = node as Element;
                  return element.id === currentHash ||
                    element.querySelector(`#${currentHash}`) !== null;
                }
                return false;
              });

              if (hasTargetElement) {
                setTimeout(handleHashChange, 100);
                mutationObserver?.disconnect();
              }
            }
          });
        });

        mutationObserver.observe(document.body, {
          childList: true,
          subtree: true
        });

        // Stop observing after 10 seconds to prevent memory leaks
        setTimeout(() => {
          mutationObserver?.disconnect();
        }, 10000);
      }
    };

    handleInitialHash();

    // Listen for hash changes
    window.addEventListener('hashchange', handleHashChange);

    // Cleanup
    return () => {
      mutationObserver?.disconnect();
      document.removeEventListener('DOMContentLoaded', handleHashChange);
      window.removeEventListener('load', handleHashChange);
      window.removeEventListener('hashchange', handleHashChange);
    };
  }, [handleHashChange]);

  /**
   * Programmatically navigate to a section
   * @param sectionId - The ID of the section to navigate to
   */
  const navigateToSection = useCallback((sectionId: string) => {
    if (typeof window === 'undefined') return;

    // Update the hash
    window.location.hash = '#' + sectionId;

    // Scroll to the element
    scrollToElement(sectionId);
  }, [scrollToElement]);

  return { navigateToSection };
}
