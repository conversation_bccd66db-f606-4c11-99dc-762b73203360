/**
 * Enhanced hash navigation utilities for static sites
 * Based on the successful AI readiness audit component approach
 */

/**
 * Navigate to a section by hash with robust error handling
 * @param hash - The hash/section ID to navigate to (without #)
 * @param retryCount - Current retry attempt
 * @param maxRetries - Maximum number of retries
 */
export function navigateToHash(
  hash: string, 
  retryCount: number = 0, 
  maxRetries: number = 3
): void {
  if (typeof window === 'undefined' || !hash) return;

  // Update the URL hash first
  if (window.location.hash !== '#' + hash) {
    window.location.hash = '#' + hash;
  }

  // Try to find and scroll to the element
  const element = document.getElementById(hash);
  if (element) {
    element.scrollIntoView({ 
      behavior: 'smooth',
      block: 'start'
    });
    return;
  }

  // Retry if element not found and we haven't exceeded max retries
  if (retryCount < maxRetries) {
    setTimeout(() => {
      navigateToHash(hash, retryCount + 1, maxRetries);
    }, 100 * (retryCount + 1)); // Exponential backoff
  } else {
    console.warn(`[HashNavigation] Could not find element with ID: ${hash}`);
  }
}

/**
 * Initialize hash navigation for a page
 * Handles initial hash on page load and sets up event listeners
 */
export function initializePageHashNavigation(): () => void {
  if (typeof window === 'undefined') return () => {};

  function handleHashChange() {
    const currentHash = window.location.hash.substring(1);
    if (currentHash) {
      navigateToHash(currentHash);
    }
  }

  // Handle initial hash on page load
  handleHashChange();

  // Listen for hash changes
  window.addEventListener('hashchange', handleHashChange);

  // Return cleanup function
  return () => {
    window.removeEventListener('hashchange', handleHashChange);
  };
}

/**
 * Generate section ID from a link string
 * @param link - The link string (e.g., "/about-us/#our-story")
 * @param fallback - Fallback ID if extraction fails
 */
export function extractSectionId(link: string, fallback?: string): string {
  if (!link) return fallback || '';
  
  const hashIndex = link.indexOf('#');
  if (hashIndex === -1) return fallback || '';
  
  return link.substring(hashIndex + 1);
}

/**
 * Ensure section IDs are properly formatted for hash navigation
 * @param title - The section title
 */
export function formatSectionId(title: string): string {
  return title
    .toLowerCase()
    .replace(/\s+/g, '-')
    .replace(/[^a-z0-9-]/g, '')
    .replace(/-+/g, '-')
    .replace(/^-|-$/g, '');
}

/**
 * Check if an element with the given ID exists in the DOM
 * @param id - The element ID to check
 */
export function elementExists(id: string): boolean {
  if (typeof window === 'undefined') return false;
  return document.getElementById(id) !== null;
}
