<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Hash Navigation Test</title>
    <style>
      body {
        font-family: Arial, sans-serif;
        margin: 20px;
        line-height: 1.6;
      }
      .test-section {
        margin: 20px 0;
        padding: 15px;
        border: 1px solid #ddd;
        border-radius: 5px;
      }
      .test-link {
        display: inline-block;
        margin: 5px 10px 5px 0;
        padding: 8px 15px;
        background: #007cba;
        color: white;
        text-decoration: none;
        border-radius: 3px;
      }
      .test-link:hover {
        background: #005a87;
      }
      .status {
        margin: 10px 0;
        padding: 10px;
        border-radius: 3px;
      }
      .success {
        background: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;
      }
      .error {
        background: #f8d7da;
        color: #721c24;
        border: 1px solid #f5c6cb;
      }
      .info {
        background: #d1ecf1;
        color: #0c5460;
        border: 1px solid #bee5eb;
      }
    </style>
  </head>
  <body>
    <h1>Hash Navigation Test for Static Site</h1>

    <div class="test-section">
      <h2>🎉 Hash Navigation Issues Fixed!</h2>
      <div class="status success">
        <strong>What was fixed:</strong>
        <ul style="margin: 10px 0; padding-left: 20px;">
          <li><strong>About Us page:</strong> All dropdown links now navigate to their correct sections instead of always going to "Our Story"</li>
          <li><strong>Careers page:</strong> Hash links now work properly and navigate to the intended sections</li>
          <li><strong>Enhanced navigation:</strong> Implemented robust hash navigation based on AI readiness audit component patterns</li>
          <li><strong>Static site compatibility:</strong> Improved reliability for AWS static hosting with multiple retry strategies</li>
        </ul>
      </div>
      <div class="status info">
        <strong>Technical improvements:</strong>
        <ul style="margin: 10px 0; padding-left: 20px;">
          <li>Enhanced HashNavigationWrapper with AI readiness audit patterns</li>
          <li>Fixed section ID mapping in About Us and Careers pages</li>
          <li>Added fallback section IDs for consistent navigation</li>
          <li>Improved hash change detection and scrolling behavior</li>
          <li>Added new hash navigation utilities for better maintainability</li>
        </ul>
      </div>
    </div>

    <div class="test-section">
      <h2>About Us Page Tests</h2>
      <p>
        Test hash navigation on the About Us page (these should now go to the
        correct sections with enhanced hash navigation):
      </p>
      <a
        href="http://localhost:3001/about-us/#our-story"
        class="test-link"
        target="_blank"
        >Our Story</a
      >
      <a
        href="http://localhost:3001/about-us/#how-we-work"
        class="test-link"
        target="_blank"
        >How We Work</a
      >
      <a
        href="http://localhost:3001/about-us/#leadership-team"
        class="test-link"
        target="_blank"
        >Leadership Team</a
      >
      <a
        href="http://localhost:3001/about-us/#in-news"
        class="test-link"
        target="_blank"
        >In News</a
      >
      <div class="status success">
        <strong>Fixed:</strong> Each link now navigates to its specific section using enhanced hash navigation based on AI readiness audit component patterns.
      </div>
    </div>

    <div class="test-section">
      <h2>Careers Page Tests</h2>
      <p>
        Test hash navigation on the Careers page (these should now work properly
        with enhanced hash navigation):
      </p>
      <a
        href="http://localhost:3001/careers/#current-opportunities"
        class="test-link"
        target="_blank"
        >Current Opportunities</a
      >
      <a
        href="http://localhost:3001/careers/#employee-testimonials"
        class="test-link"
        target="_blank"
        >Employee Testimonials</a
      >
      <a
        href="http://localhost:3001/careers/#core-values"
        class="test-link"
        target="_blank"
        >Core Values</a
      >
      <a
        href="http://localhost:3001/careers/#benefits"
        class="test-link"
        target="_blank"
        >Benefits</a
      >
      <a
        href="http://localhost:3001/careers/#life-at-mtl"
        class="test-link"
        target="_blank"
        >Life at MTL</a
      >
      <div class="status success">
        <strong>Fixed:</strong> Links now navigate to correct sections using enhanced hash navigation with proper section ID mapping and fallbacks.
      </div>
    </div>

    <div class="test-section">
      <h2>AI Readiness Page Tests (Control)</h2>
      <p>
        Test hash navigation on the AI Readiness page (this should work as it
        did before):
      </p>
      <a
        href="http://localhost:3001/ai-readiness-audit/"
        class="test-link"
        target="_blank"
        >AI Readiness Audit</a
      >
    </div>

    <div class="test-section">
      <h2>Automated Test Results</h2>
      <div id="test-results">
        <div class="info">
          Click "Run Automated Tests" to test hash navigation programmatically.
        </div>
      </div>
      <button
        onclick="runHashNavigationTests()"
        style="
          padding: 10px 20px;
          background: #28a745;
          color: white;
          border: none;
          border-radius: 3px;
          cursor: pointer;
        "
      >
        Run Automated Tests
      </button>
    </div>

    <script>
      async function runHashNavigationTests() {
        const resultsDiv = document.getElementById('test-results');
        resultsDiv.innerHTML = '<div class="info">Running tests...</div>';

        const tests = [
          {
            url: 'http://localhost:3001/about-us/',
            hash: 'our-story',
            name: 'About Us - Our Story',
          },
          {
            url: 'http://localhost:3001/about-us/',
            hash: 'leadership-team',
            name: 'About Us - Leadership Team',
          },
          {
            url: 'http://localhost:3001/careers/',
            hash: 'core-values',
            name: 'Careers - Core Values',
          },
          {
            url: 'http://localhost:3001/careers/',
            hash: 'benefits',
            name: 'Careers - Benefits',
          },
        ];

        let results = [];

        for (const test of tests) {
          try {
            const result = await testHashNavigation(
              test.url,
              test.hash,
              test.name,
            );
            results.push(result);
          } catch (error) {
            results.push({
              name: test.name,
              success: false,
              message: `Error: ${error.message}`,
            });
          }
        }

        displayResults(results);
      }

      async function testHashNavigation(baseUrl, hash, testName) {
        return new Promise(resolve => {
          const iframe = document.createElement('iframe');
          iframe.style.display = 'none';
          document.body.appendChild(iframe);

          let resolved = false;
          const timeout = setTimeout(() => {
            if (!resolved) {
              resolved = true;
              document.body.removeChild(iframe);
              resolve({
                name: testName,
                success: false,
                message: 'Timeout - page took too long to load',
              });
            }
          }, 10000);

          iframe.onload = () => {
            setTimeout(() => {
              if (!resolved) {
                resolved = true;
                clearTimeout(timeout);

                try {
                  // Navigate to hash
                  iframe.contentWindow.location.hash = '#' + hash;

                  setTimeout(() => {
                    try {
                      const element =
                        iframe.contentDocument.getElementById(hash);
                      const success = element !== null;

                      document.body.removeChild(iframe);
                      resolve({
                        name: testName,
                        success: success,
                        message: success
                          ? 'Element found and hash navigation should work'
                          : `Element with ID "${hash}" not found`,
                      });
                    } catch (error) {
                      document.body.removeChild(iframe);
                      resolve({
                        name: testName,
                        success: false,
                        message: `Cross-origin error (expected in local testing): ${error.message}`,
                      });
                    }
                  }, 1000);
                } catch (error) {
                  document.body.removeChild(iframe);
                  resolve({
                    name: testName,
                    success: false,
                    message: `Error accessing iframe: ${error.message}`,
                  });
                }
              }
            }, 2000);
          };

          iframe.src = baseUrl;
        });
      }

      function displayResults(results) {
        const resultsDiv = document.getElementById('test-results');
        let html = '<h3>Test Results:</h3>';

        results.forEach(result => {
          const statusClass = result.success ? 'success' : 'error';
          html += `
                    <div class="${statusClass}">
                        <strong>${result.name}:</strong> ${result.message}
                    </div>
                `;
        });

        const successCount = results.filter(r => r.success).length;
        html += `
                <div class="info">
                    <strong>Summary:</strong> ${successCount}/${results.length} tests passed
                </div>
            `;

        resultsDiv.innerHTML = html;
      }
    </script>
  </body>
</html>
